"""
Pydantic schemas for main API service
"""

from pydantic import BaseModel, EmailStr
from typing import List, Optional
from datetime import datetime
from shared.schemas.extraction import InvoiceLineItemData, InvoiceAllowanceChargeData, InvoicePaymentTermData

class ProcessEmailsRequest(BaseModel):
    """
    Request schema for processing emails through the complete pipeline
    """
    email_host: str
    email_port: int = 993
    email_username: EmailStr
    email_password: str
    use_ssl: bool = True
    folder: str = "INBOX"
    date_filter: Optional[str] = None
    max_emails: Optional[int] = 100

class CUFEResponse(BaseModel):
    """
    Response schema for comprehensive CUFE and invoice information
    """
    cufe_value: str
    email_id: str
    reception_date: datetime
    xml_file_path: str
    pdf_file_path: Optional[str] = None
    processed_date: datetime

    # Basic invoice information
    issuer_name: Optional[str] = None
    document_number: Optional[str] = None
    issue_date: Optional[datetime] = None
    total_amount: Optional[str] = None

    # Enhanced tax and monetary details
    tax_exclusive_amount: Optional[str] = None
    tax_inclusive_amount: Optional[str] = None
    allowance_total_amount: Optional[str] = None
    charge_total_amount: Optional[str] = None
    prepaid_amount: Optional[str] = None
    payable_amount: Optional[str] = None

    # Tax breakdown details
    total_tax_amount: Optional[str] = None
    iva_amount: Optional[str] = None
    rete_fuente_amount: Optional[str] = None
    rete_iva_amount: Optional[str] = None
    rete_ica_amount: Optional[str] = None

    # Additional invoice details
    due_date: Optional[datetime] = None
    currency_code: Optional[str] = None
    invoice_type_code: Optional[str] = None
    accounting_cost: Optional[str] = None

    # Related data collections
    line_items: Optional[List[InvoiceLineItemData]] = None
    allowance_charges: Optional[List[InvoiceAllowanceChargeData]] = None
    payment_terms: Optional[List[InvoicePaymentTermData]] = None

class CUFEListResponse(BaseModel):
    """
    Response schema for listing CUFE records
    """
    records: List[CUFEResponse]
    total: int
    skip: int
    limit: int

class ProcessingStatus(BaseModel):
    """
    Schema for processing status information
    """
    status: str  # pending, processing, completed, failed
    message: str
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    processed_emails: int = 0
    extracted_cufes: int = 0
    errors: Optional[List[str]] = None

class ServiceHealth(BaseModel):
    """
    Schema for service health information
    """
    service_name: str
    status: str  # healthy, unhealthy, degraded
    last_check: datetime
    response_time_ms: Optional[float] = None
    error_message: Optional[str] = None

class SystemStatus(BaseModel):
    """
    Schema for overall system status
    """
    overall_status: str
    services: List[ServiceHealth]
    last_updated: datetime
