"""
Pydantic schemas for extraction service
"""

from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime

class ExtractionRequest(BaseModel):
    """
    Request schema for CUFE extraction
    """
    xml_file_path: str
    email_id: Optional[str] = None
    extract_additional_data: bool = True

class InvoiceLineItemData(BaseModel):
    """
    Schema for individual invoice line item data
    """
    line_number: Optional[int] = None
    item_name: Optional[str] = None
    item_description: Optional[str] = None
    item_code: Optional[str] = None
    invoiced_quantity: Optional[str] = None
    unit_of_measure: Optional[str] = None
    unit_price: Optional[str] = None
    line_extension_amount: Optional[str] = None
    line_tax_amount: Optional[str] = None
    line_tax_inclusive_amount: Optional[str] = None
    allowance_charge_amount: Optional[str] = None
    free_of_charge_indicator: Optional[bool] = False

class InvoiceAllowanceChargeData(BaseModel):
    """
    Schema for invoice allowance/charge data
    """
    charge_indicator: bool  # True for charge, False for allowance
    allowance_charge_reason_code: Optional[str] = None
    allowance_charge_reason: Optional[str] = None
    multiplier_factor_numeric: Optional[str] = None
    amount: Optional[str] = None
    base_amount: Optional[str] = None
    tax_category: Optional[str] = None
    tax_amount: Optional[str] = None

class InvoicePaymentTermData(BaseModel):
    """
    Schema for payment terms data
    """
    payment_means_code: Optional[str] = None
    payment_due_date: Optional[datetime] = None
    payment_terms_note: Optional[str] = None
    settlement_period_measure: Optional[str] = None
    settlement_period_unit: Optional[str] = None
    settlement_discount_percent: Optional[str] = None
    penalty_surcharge_percent: Optional[str] = None
    amount: Optional[str] = None

class CUFEData(BaseModel):
    """
    Schema for CUFE and comprehensive extracted invoice data
    """
    cufe_value: str
    issuer_name: Optional[str] = None
    document_number: Optional[str] = None
    issue_date: Optional[datetime] = None
    total_amount: Optional[str] = None

    # Enhanced tax and monetary details
    tax_exclusive_amount: Optional[str] = None
    tax_inclusive_amount: Optional[str] = None
    allowance_total_amount: Optional[str] = None
    charge_total_amount: Optional[str] = None
    prepaid_amount: Optional[str] = None
    payable_amount: Optional[str] = None

    # Tax breakdown details
    total_tax_amount: Optional[str] = None
    iva_amount: Optional[str] = None
    rete_fuente_amount: Optional[str] = None
    rete_iva_amount: Optional[str] = None
    rete_ica_amount: Optional[str] = None

    # Additional invoice details
    due_date: Optional[datetime] = None
    currency_code: Optional[str] = None
    invoice_type_code: Optional[str] = None
    accounting_cost: Optional[str] = None

    # Related data collections
    line_items: Optional[List[InvoiceLineItemData]] = None
    allowance_charges: Optional[List[InvoiceAllowanceChargeData]] = None
    payment_terms: Optional[List[InvoicePaymentTermData]] = None

    additional_fields: Optional[Dict[str, Any]] = None

class ExtractionResponse(BaseModel):
    """
    Response schema for CUFE extraction
    """
    success: bool
    cufe_value: str
    xml_file_path: str
    message: str
    cufe_data: Optional[CUFEData] = None
    extraction_date: datetime = datetime.now()
    errors: Optional[List[str]] = None

class BatchExtractionRequest(BaseModel):
    """
    Request schema for batch CUFE extraction
    """
    xml_file_paths: List[str]
    email_ids: Optional[List[str]] = None

class BatchExtractionResult(BaseModel):
    """
    Schema for individual batch extraction result
    """
    xml_file_path: str
    success: bool
    cufe_value: Optional[str] = None
    cufe_data: Optional[CUFEData] = None
    error_message: Optional[str] = None

class BatchExtractionResponse(BaseModel):
    """
    Response schema for batch CUFE extraction
    """
    success: bool
    message: str
    processed_count: int
    successful_extractions: int
    failed_extractions: int
    results: List[BatchExtractionResult]
